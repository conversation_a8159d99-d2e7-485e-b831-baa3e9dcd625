import json
import os
import time

import httpx
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel, ValidationError

from dc_ai_red_line_review.core import CoreComponents
from dc_ai_red_line_review.utils import (
    ChonkieTextChunker,
    get_logger,
    get_token_count,
    timing_decorator,
)

load_dotenv(override=True)


CHUNK_SIZE_TOKENS = 8_000
OVERLAP_TOKENS = 1_000


# Pydantic model for message validation
class MessageItem(BaseModel):
    id: int  # change from str to int to accept numeric IDs
    type: str
    msg: str


class BasicPipeline:
    def __init__(self):
        self.logger = get_logger(module_name="red_line_review")

        if os.environ.get("PROMPT_SOURCE") == "local":
            with open(os.path.join(os.environ["PROMPT_PATH"], "prompt.json")) as file:
                self.prompt_dict = json.load(file)
        else:
            self.prompt_dict = json.loads(os.environ["PROMPT_DICT"])

        self.logger.info("Initializing pipeline components")

        self.model_client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

        self.core_components = CoreComponents(
            model_client=self.model_client, prompt_dict=self.prompt_dict
        )

        # Initialize Chonkie text chunker
        self.text_chunker = ChonkieTextChunker(
            chunk_size=CHUNK_SIZE_TOKENS, chunk_overlap=OVERLAP_TOKENS
        )
        self.logger.info("Initialized Chonkie text chunker")

    def _split_content_to_token_limit(
        self, content_to_split: str, limit: int
    ) -> tuple[list[str], list[int]]:
        """简化的内容分块方法，使用Chonkie进行分块并打印token信息."""
        if not content_to_split:
            return [], []

        print(f"\n🔪 开始分块: {len(content_to_split):,} 字符")

        try:
            # 使用Chonkie进行分块
            chunks, chunk_tokens = self.text_chunker.chunk_text(content_to_split)

            print("📊 分块结果:")
            print(f"  - 分块数量: {len(chunks)}")
            print(f"  - 各块token数: {chunk_tokens}")
            print(f"  - 总token数: {sum(chunk_tokens):,}")

            # 打印每个分块的详细信息
            for i, (chunk, tokens) in enumerate(zip(chunks, chunk_tokens)):
                print(f"  📄 分块 {i + 1}: {len(chunk):,} 字符, {tokens:,} tokens")

            return chunks, chunk_tokens

        except Exception as e:
            print(f"❌ Chonkie分块失败: {e}")
            # 简单fallback：使用get_token_count计算
            estimated_tokens = get_token_count(content_to_split)
            print(f"📄 Fallback: 单个分块, {estimated_tokens:,} tokens")
            return [content_to_split], [estimated_tokens]

    def _split_messages_by_conversation(
        self, messages: list, token_limit: int
    ) -> tuple[list[list], list[int]]:
        """Split messages into chunks by conversation turns while respecting token limits.

        This method preserves conversation context by keeping complete message exchanges
        together and provides intelligent overlap based on conversation flow.

        Args:
            messages: List of message dictionaries with 'id', 'type', 'msg' fields
            token_limit: Maximum tokens per chunk

        Returns:
            tuple: (list of message chunks, list of token counts for each chunk)
        """
        if not messages:
            return [], []

        print(f"\n🔪 开始消息分块: {len(messages)} 条消息")

        try:
            # 使用Chonkie的消息分块功能
            message_chunks, chunk_tokens = self.text_chunker.chunk_messages(
                messages, token_limit
            )

            print("📊 消息分块结果:")
            print(f"  - 分块数量: {len(message_chunks)}")
            print(f"  - 各块token数: {chunk_tokens}")
            print(f"  - 总token数: {sum(chunk_tokens):,}")

            # 打印每个分块的详细信息
            for i, (msg_chunk, tokens) in enumerate(zip(message_chunks, chunk_tokens)):
                first_id = msg_chunk[0]["id"] if msg_chunk else "N/A"
                last_id = msg_chunk[-1]["id"] if msg_chunk else "N/A"
                print(
                    f"  📄 分块 {i + 1}: {len(msg_chunk)} 消息 (ID {first_id}-{last_id}), {tokens:,} tokens"
                )

            return message_chunks, chunk_tokens

        except Exception as e:
            print(f"❌ Chonkie消息分块失败: {e}")
            # 简单fallback：所有消息作为一个分块
            total_tokens = sum(
                get_token_count(f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>")
                for msg in messages
            )
            print(
                f"📄 Fallback: 单个分块, {len(messages)} 消息, {total_tokens:,} tokens"
            )
            return [messages], [total_tokens]

    def _format_message_chunk_to_content(self, message_chunk: list) -> str:
        """Convert a chunk of messages back to formatted content string."""
        if not message_chunk:
            return ""

        formatted_parts = [
            f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
            for msg in message_chunk
        ]
        return "\n".join(formatted_parts)

    def _merge_review_results(self, list_of_chunk_results: list[dict]) -> dict:
        """Merges results from multiple chunks. For each category, results are collected and deduplicated."""
        # Early returns for simple cases
        if not list_of_chunk_results:
            return {}
        if len(list_of_chunk_results) == 1:
            return list_of_chunk_results[0]  # No merge needed if only one chunk

        self.logger.info(
            f"Merging review results from {len(list_of_chunk_results)} chunks"
        )

        # Extract all unique categories from valid dictionaries
        all_categories = set().union(
            *(res.keys() for res in list_of_chunk_results if isinstance(res, dict))
        )

        # Build merged results dictionary with deduplication for each category
        merged_results = {}
        for category in all_categories:
            # Collect all results for this category
            category_results = [
                res[category]
                for res in list_of_chunk_results
                if isinstance(res, dict) and category in res
            ]

            # Deduplicate results based on their content
            if category_results:
                if isinstance(category_results[0], list):
                    # If it's a list of items, merge and deduplicate
                    all_items = []
                    for result_list in category_results:
                        all_items.extend(result_list)
                    # Deduplicate based on string representation of the item
                    seen = set()
                    deduplicated_items = []
                    for item in all_items:
                        item_str = (
                            str(sorted(item.items()))
                            if isinstance(item, dict)
                            else str(item)
                        )
                        if item_str not in seen:
                            seen.add(item_str)
                            deduplicated_items.append(item)
                    merged_results[category] = deduplicated_items
                elif isinstance(category_results[0], dict):
                    # If it's a single dict, merge values and deduplicate
                    merged_dict = {"values": []}
                    all_values = []
                    for result_dict in category_results:
                        if "values" in result_dict:
                            all_values.extend(result_dict["values"])
                    # Deduplicate values
                    merged_dict["values"] = list(set(all_values)) if all_values else []
                    # Copy other fields from the first result
                    for key, value in category_results[0].items():
                        if key != "values":
                            merged_dict[key] = value
                    merged_results[category] = merged_dict
                else:
                    # For other types, just take the first result
                    merged_results[category] = category_results[0]

        return merged_results

    @timing_decorator
    def _validate_and_sort_messages(self, messages, caseId):
        """Validate and sort messages by 'id' using Pydantic. Raises ValueError if invalid."""
        if not isinstance(messages, list):
            raise ValueError(
                f"caseId {caseId}: 'messages' must be a list, got {type(messages)}"
            )
        if not messages:
            self.logger.warning(f"caseId {caseId}: empty messages list")
            return []
        try:
            msgs = [MessageItem(**msg) for msg in messages]
        except ValidationError as e:
            raise ValueError(f"caseId {caseId}: invalid messages data - {e}")
        # sort by id and convert to dict
        return [m.model_dump() for m in sorted(msgs, key=lambda x: x.id)]

    def _process_values_and_matched_ids(self, item_with_values, messages):
        """Process a single item containing values, add matched_ids and deduplicate.

        Only keep values that can be found in the original messages.

        Args:
            item_with_values: Dictionary containing values field
            messages: Message list
        """
        # First deduplicate values while maintaining order
        unique_values = list(dict.fromkeys(item_with_values["values"]))

        # Only keep values that can be matched in original messages
        validated_values = []
        validated_matched_ids = []

        for value in unique_values:
            ids = [msg["id"] for msg in messages if value in msg["msg"]]
            if ids:  # Only keep values that have matches in original text
                validated_values.append(value)
                # Deduplicate ids for each value
                unique_ids = list(dict.fromkeys(ids))  # Order-preserving deduplication
                validated_matched_ids.append(unique_ids)
                self.logger.debug(
                    f"Validated value: '{value}' -> matched IDs: {unique_ids}"
                )
            else:
                self.logger.warning(
                    f"Discarded unverifiable value: '{value}' (not found in original messages)"
                )

        # Update with only validated values
        item_with_values["values"] = validated_values
        item_with_values["matched_ids"] = validated_matched_ids

        # Update hit_rule based on whether we have any validated values
        if not validated_values:
            item_with_values["hit_rule"] = False
            self.logger.info("No validated values found, setting hit_rule to False")

    def attach_matched_ids(self, review_res, messages):
        """Add matched_ids field to each review item with values, structured as a 2D array. Add matched_ids: [] even if values is empty.

        Deduplicate values and matched_ids to avoid duplicate content and IDs.
        """
        for category, items in review_res.items():
            if isinstance(items, list):
                for item in items:
                    if "values" in item:
                        self._process_values_and_matched_ids(item, messages)
            elif isinstance(items, dict) and "values" in items:
                self._process_values_and_matched_ids(items, messages)
        return review_res

    @timing_decorator
    def run(self, messages: list, caseId: str = ""):
        """Process and analyze messages, handling chunking and language detection."""
        self.logger.info(
            f"Processing caseId: {caseId} with {len(messages) if isinstance(messages, list) else 0} messages"
        )

        # Step 1: Validate and prepare messages
        try:
            msgs = self._validate_and_sort_messages(messages, caseId)
            if not msgs:
                return {"id": caseId, "review_res": {}}

            # Format messages into model-ready format
            formatted_parts = [
                f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in msgs
            ]
            full_content = "\n".join(formatted_parts)

            # 添加详细的内容长度日志
            self.logger.info(f"Content statistics for caseId {caseId}:")
            self.logger.info(f"  - Total messages: {len(msgs)}")
            self.logger.info(f"  - Total characters: {len(full_content):,}")
            self.logger.info(
                f"  - Average chars per message: {len(full_content) // len(msgs) if msgs else 0}"
            )

            # 显示每条消息的长度并检查单条消息token限制
            for i, msg in enumerate(msgs):
                msg_len = len(msg["msg"])
                # 计算单条消息的token数量（包含格式化）
                formatted_msg = f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
                msg_tokens = get_token_count(formatted_msg)

                # 检查单条消息是否超过15000 token限制
                if msg_tokens > 15000:
                    error_msg = (
                        f"Single message (ID: {msg['id']}) exceeds 15000 token limit: "
                        f"{msg_tokens:,} tokens. Message content length: {msg_len:,} characters."
                    )
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                self.logger.debug(
                    f"  Message {i + 1} (ID: {msg['id']}): {msg_len:,} chars, ~{msg_tokens:,} tokens"
                )

            self.logger.debug(f"Content prepared for caseId {caseId}: \n{full_content}")

        except ValueError as e:
            raise ValueError(f"Message validation failed: {e}")

        # Step 2: Handle content size and chunking
        try:
            # Calculate tokens once for the full content
            total_tokens = get_token_count(full_content)
            self.logger.info(f"Content tokens: {total_tokens:,} (estimated)")
            self.logger.info(f"Chunk size limit: {CHUNK_SIZE_TOKENS:,} tokens")
            self.logger.info(
                f"Character to token ratio: {len(full_content) / total_tokens:.2f} chars/token"
            )

            # Determine if chunking is needed
            if total_tokens > CHUNK_SIZE_TOKENS:
                self.logger.warning(
                    f"Content exceeds chunk size limit ({total_tokens:,} > {CHUNK_SIZE_TOKENS:,}). "
                    f"Using conversation-aware chunking..."
                )

                # Use conversation-aware chunking instead of character-based chunking
                message_chunks, chunk_tokens = self._split_messages_by_conversation(
                    msgs, CHUNK_SIZE_TOKENS
                )

                if not message_chunks:
                    raise ValueError(
                        f"Conversation chunking failed for {len(msgs)} messages with {total_tokens} tokens"
                    )

                # Convert message chunks back to formatted content strings
                chunks = []
                for i, msg_chunk in enumerate(message_chunks):
                    chunk_content = self._format_message_chunk_to_content(msg_chunk)
                    chunks.append(chunk_content)

                    # Verify token count matches
                    actual_tokens = get_token_count(chunk_content)
                    if (
                        abs(actual_tokens - chunk_tokens[i]) > 50
                    ):  # Allow small variance
                        self.logger.warning(
                            f"Chunk {i + 1} token count mismatch: "
                            f"calculated {chunk_tokens[i]}, actual {actual_tokens}"
                        )
                        chunk_tokens[i] = actual_tokens

                # 显示分块详情
                self.logger.info(
                    f"Content split into {len(chunks)} conversation-aware chunks:"
                )
                for i, (chunk, tokens, msg_chunk) in enumerate(
                    zip(chunks, chunk_tokens, message_chunks)
                ):
                    first_msg_id = msg_chunk[0]["id"] if msg_chunk else "N/A"
                    last_msg_id = msg_chunk[-1]["id"] if msg_chunk else "N/A"
                    self.logger.info(
                        f"  Chunk {i + 1}: {len(msg_chunk)} messages (ID {first_msg_id}-{last_msg_id}), "
                        f"{len(chunk):,} chars, ~{tokens:,} tokens"
                    )
            else:
                chunks = [full_content]
                chunk_tokens = [total_tokens]
                self.logger.info(
                    f"Content fits in single chunk ({total_tokens:,} <= {CHUNK_SIZE_TOKENS:,} tokens)"
                )
        except Exception as e:
            self.logger.error(f"Token calculation or chunking failed: {e}")
            raise ValueError(f"Content processing failed: {e}")

        # Step 3: Process all chunks sequentially
        self.logger.info(f"Starting sequential processing of {len(chunks)} chunks")

        results = []
        for i, (chunk, tokens) in enumerate(zip(chunks, chunk_tokens)):
            self.logger.info(
                f"Processing chunk {i + 1}/{len(chunks)} (~{tokens} tokens)"
            )

            chunk_result = self.run_sync(chunk, tokens)

            self.logger.info(
                f"Completed chunk {i + 1}/{len(chunks)} (~{tokens} tokens)"
            )

            results.append(chunk_result)

        # Step 4: Merge results and detect language
        merged_results = self._merge_review_results(results)
        merged_results = self.attach_matched_ids(merged_results, msgs)

        return {"id": caseId, "review_res": merged_results}

    def run_sync(self, content, content_tokens=None):
        """Run all review tasks sequentially and merge results."""
        result_dict = {}

        # Use pre-calculated token count or calculate once if not provided
        if content_tokens is None:
            content_tokens = get_token_count(content) if content else 0

        self.logger.info(
            f"Starting sequential execution of review tasks with {content_tokens:,} tokens"
        )

        # Execute tasks sequentially
        try:
            # Task 1: Key contact review
            self.logger.info("Executing task: key_contact")
            start_time = time.time()
            result_dict["key_contact"] = self.core_components.key_contact_review(
                content=content, risk_keywords=self.prompt_dict["key_contact"]
            )
            self.logger.info(
                f"Task key_contact completed in {time.time() - start_time:.2f}s"
            )

            # Task 2: Unified sensitive content review (main API call)
            self.logger.info("Executing task: unified_sensitive_review")
            start_time = time.time()
            unified_result = self.core_components.unified_all_review_sync(
                content, content_tokens
            )
            self.logger.info(
                f"Task unified_sensitive_review completed in {time.time() - start_time:.2f}s"
            )

            # Task 3: Government inquiry review
            self.logger.info("Executing task: government_inquiry")
            start_time = time.time()
            result_dict["government_inquiry"] = (
                self.core_components.government_inquiry_review(
                    content, self.prompt_dict["government_inquiry"]
                )
            )
            self.logger.info(
                f"Task government_inquiry completed in {time.time() - start_time:.2f}s"
            )

            # Task 4: Internal system review
            self.logger.info("Executing task: internal_system")
            start_time = time.time()
            result_dict["internal_system"] = self.core_components.key_contact_review(
                content=content,
                risk_keywords=self.prompt_dict["internal_system"],
            )
            self.logger.info(
                f"Task internal_system completed in {time.time() - start_time:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"Error during sequential task execution: {e}")
            # Ensure we have some result structure even if tasks fail
            if "key_contact" not in result_dict:
                result_dict["key_contact"] = {}
            if "government_inquiry" not in result_dict:
                result_dict["government_inquiry"] = []
            if "internal_system" not in result_dict:
                result_dict["internal_system"] = {}
            unified_result = {"sensitive_inquiry": [], "sensitive_reply": []}

        self.logger.info("All review tasks completed sequentially")

        # Process unified review results and split back to original format
        if isinstance(unified_result, dict):
            # Extract sensitive_inquiry and sensitive_reply from unified result
            if "sensitive_inquiry" in unified_result:
                result_dict["sensitive_inquiry"] = unified_result["sensitive_inquiry"]
            else:
                result_dict["sensitive_inquiry"] = []

            if "sensitive_reply" in unified_result:
                result_dict["sensitive_reply"] = unified_result["sensitive_reply"]
            else:
                result_dict["sensitive_reply"] = []
        else:
            # Fallback if unified result format is unexpected
            self.logger.warning("Unified result format unexpected, using empty results")
            result_dict["sensitive_inquiry"] = []
            result_dict["sensitive_reply"] = []

        return result_dict


if __name__ == "__main__":
    # Use context manager to ensure proper resource cleanup
    # with BasicPipeline() as pipeline:
    pipeline = BasicPipeline()
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    from pprint import pprint

    pprint(pipeline.run(messages=messages, caseId=caseId))
